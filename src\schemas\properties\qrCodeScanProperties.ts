import { NUMBER_LIMIT } from "@/lib/constants";
import { z } from "zod";

export const qrCodeScanPropertiesSchema = z.object({
  validationType: z.string(),
  allowDecimals: z.boolean().optional(),
  currency: z.boolean().optional(),
  includeTimeValidation: z.boolean().optional(),
  minimumValue: z.number({ message: "Enter minimum value" }).optional(),
  maximumValue: z.number({ message: "Enter maximum value" }).optional(),
  minimumLength: z.number({ message: "Enter minimum length" }).optional(),
  maximumLength: z.number({ message: "Enter maximum length" }).optional(),
  minimumDateRange: z.number({ message: "Enter minimum date" }).optional(),
  maximumDateRange: z.number({ message: "Enter maximum date" }).optional(),
  // minimumDateRange: z.string().optional(),
  // maximumDateRange: z.string().optional(),
});
// .superRefine(({ minimumValue, maximumValue }, ctx) => {
//   if (minimumValue >= maximumValue) {
//     ctx.addIssue({
//       code: z.ZodIssueCode.custom,
//       message: "Minimum value must be less than maximum value",
//       path: ["minimumValue"],
//     });
//   }
// });
