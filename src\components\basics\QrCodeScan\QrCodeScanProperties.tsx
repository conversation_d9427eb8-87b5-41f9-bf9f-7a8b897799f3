import { QrCodeScanPropertiesFormData } from "@/app/(core)/forms/types";
import NumberInput from "@/components/NumberInput";
import { FormElement } from "@/components/types";
import { Checkbox } from "@/components/ui/checkbox";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useGetSingleForm } from "@/hooks/tansack-query/queries/use-forms";
import { useAppDispatch, useAppSelector } from "@/hooks/use-redux";
import { replaceFormElement } from "@/lib/redux/slices/formSlice";
import { SelectedFormElementPayload } from "@/lib/redux/types";
import { findFormElementSectionId } from "@/lib/utils";
import { qrCodeScanPropertiesSchema } from "@/schemas/properties/qrCodeScanProperties";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect } from "react";
import DatePicker from "@/components/DatePicker";
import { useForm } from "react-hook-form";

const QrCodeScanProperties = () => {
  const dispatch = useAppDispatch();
  const selectedFormBuilderItem = useAppSelector(state => state.form.selectedFormBuilderItem) as SelectedFormElementPayload;
  const screenId = useAppSelector(state => state.form.selectedFormBuilderItemScreen);
  const formScreens = useAppSelector(state => state.form.formScreens);
  const formId = useAppSelector(state => state.form.formId);
  const elementId = selectedFormBuilderItem?.id ?? "";
  const elementScreen = formScreens.find(screen => screen.id === screenId);
  let sectionId = "";
  if (elementScreen) {
    sectionId = findFormElementSectionId(elementScreen.sections, elementId) ?? "";
  }

  const { singleFormData } = useGetSingleForm(formId);
  const validationTypes = ["Allow all types", "Text", "Number", "Email", "URL", "Phone Number", "Date"];

  const form = useForm<QrCodeScanPropertiesFormData>({
    resolver: zodResolver(qrCodeScanPropertiesSchema),
    mode: "onChange",
    defaultValues: {
      validationType: selectedFormBuilderItem?.validationType,
    },
    shouldFocusError: false,
  });

  const {
    setValue,
    watch,
    formState: { errors },
  } = form;

  useEffect(() => {
    setValue("validationType", selectedFormBuilderItem?.validationType as string);
  }, [selectedFormBuilderItem]);

  const updateFormElements = (data: QrCodeScanPropertiesFormData) => {
    const newFormElement = {
      ...selectedFormBuilderItem,
      ...data,
    } as FormElement;
    dispatch(replaceFormElement({ screenId, sectionId, element: newFormElement }));
  };

  return (
    <Form {...form}>
      <form onChange={form.handleSubmit(updateFormElements)}>
        <div className="flex flex-col gap-2">
          <FormField
            control={form.control}
            name="validationType"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Validation Type</FormLabel>
                <Select value={field.value} onValueChange={field.onChange}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {validationTypes.map(type => (
                      <SelectItem value={type} key={type}>
                        {type}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
          {watch("validationType") === "Number" && (
            <>
              <p>Values</p>
              <div className="flex items-center gap-2">
                <FormField
                  control={form.control}
                  name="minimumValue"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <NumberInput
                          {...field}
                          value={field.value}
                          placeholder="Min"
                          onChange={field.onChange}
                          className={`${errors.minimumValue && "border-destructive"}`}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="maximumValue"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <NumberInput
                          {...field}
                          value={field.value}
                          placeholder="Max"
                          onChange={field.onChange}
                          className={`${errors.maximumValue && "border-destructive"}`}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <FormField
                control={form.control}
                name="allowDecimals"
                render={({ field }) => (
                  <FormItem>
                    <div className="flex items-center gap-1">
                      <FormControl>
                        <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                      <FormLabel>Allow decimal numbers</FormLabel>
                    </div>
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="currency"
                render={({ field }) => (
                  <FormItem>
                    <div className="flex items-center gap-1">
                      <FormControl>
                        <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                      <FormLabel>Currency</FormLabel>
                    </div>
                  </FormItem>
                )}
              />
            </>
          )}
          {watch("validationType") === "Phone Number" && (
            <>
              <p>Length</p>
              <div className="flex items-center gap-2">
                <FormField
                  control={form.control}
                  name="minimumLength"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <NumberInput
                          {...field}
                          value={field.value}
                          placeholder="Min Digits"
                          onChange={field.onChange}
                          className={`${errors.minimumLength && "border-destructive"}`}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="maximumLength"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <NumberInput
                          {...field}
                          value={field.value}
                          placeholder="Max Digits"
                          onChange={field.onChange}
                          className={`${errors.maximumLength && "border-destructive"}`}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <p>Country Code</p>
              <div className="h-[3rem] rounded-[10px] border border-primary-gray bg-primary-gray/10 p-3">{singleFormData?.country?.phone_code}</div>
            </>
          )}
          {watch("validationType") === "Date" && (
            <>
              <FormField
                control={form.control}
                name="includeTimeValidation"
                render={({ field }) => (
                  <FormItem>
                    <div className="flex items-center gap-1">
                      <FormControl>
                        <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                      <FormLabel>Include time validation</FormLabel>
                    </div>
                  </FormItem>
                )}
              />
              <p>Date Range</p>
              <div className="flex items-center gap-2">
                <FormField
                  control={form.control}
                  name="minimumDateRange"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <DatePicker
                          {...field}
                          value={field.value}
                          placeholder="Min Date"
                          onChange={field.onChange}
                          className={`${errors.minimumLength && "border-destructive"}`}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="maximumDateRange"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <DatePicker
                          {...field}
                          value={field.value}
                          placeholder="Max Date"
                          onChange={field.onChange}
                          className={`${errors.maximumLength && "border-destructive"}`}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </>
          )}
        </div>
      </form>
    </Form>
  );
};

export default QrCodeScanProperties;
