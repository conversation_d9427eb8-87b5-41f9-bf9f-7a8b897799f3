import Hint from "@/components/Hint";
import LabelInput from "@/components/LabelInput";
import { BaseFieldProps } from "@/components/types";
import { updateFormElementOnInputChange } from "@/lib/utils";
import React from "react";
import { fredoka } from "@/app/fonts";
import Image from "next/image";
import QRCodeImage from "@/assets/icons/qr-code.svg";

const QrCodeScanField = ({ element, screenId, sectionId }: BaseFieldProps) => {
  const { label, hint, tooltip, required } = element;

  const handleLabelChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    updateFormElementOnInputChange({ label: e.target.value }, screenId, sectionId);
  };

  return (
    <div className="relative space-y-6 px-6 pb-8 pt-6">
      <div>
        <div className="space-y-4">
          <LabelInput label={label} required={required} tooltip={tooltip} onLabelChange={handleLabelChange} />
          <div className="rounded-lg border-2 border-dashed border-primary-green p-4">
            <h3 className={`text-center text-lg font-semibold leading-loose ${fredoka.className}`}>Scan the QR Code</h3>
            <div className="bg-[#C4C4C4] px-4 py-8">
              <div className="mx-auto flex h-48 w-48 items-center justify-center">
                <Image src={QRCodeImage} alt="QR Code" />
              </div>
            </div>
          </div>
        </div>
        {hint && <Hint text={hint} />}
      </div>
    </div>
  );
};

export default QrCodeScanField;
